"""
用户服务类
"""

from datetime import datetime
from typing import Optional, List
from sqlalchemy.orm import Session
from sqlalchemy import func

from app.models.user import User, UserRole
from app.schemas.user import UserCreate, UserUpdate
from app.core.security import get_password_hash, verify_password


class UserService:
    """用户服务类"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get(self, user_id: int) -> Optional[User]:
        """根据ID获取用户"""
        return self.db.query(User).filter(User.user_id == user_id).first()
    
    def get_by_username(self, username: str) -> Optional[User]:
        """根据用户名获取用户"""
        return self.db.query(User).filter(User.username == username).first()
    
    def get_by_email(self, email: str) -> Optional[User]:
        """根据邮箱获取用户"""
        return self.db.query(User).filter(User.email == email).first()
    
    def get_multi(self, skip: int = 0, limit: int = 100) -> List[User]:
        """获取用户列表"""
        return self.db.query(User).offset(skip).limit(limit).all()
    
    def count(self) -> int:
        """获取用户总数"""
        return self.db.query(func.count(User.user_id)).scalar()
    
    def create(self, user_in: UserCreate) -> User:
        """创建用户"""
        # 创建用户对象
        user = User(
            username=user_in.username,
            password_hash=get_password_hash(user_in.password),
            email=user_in.email,
            full_name=user_in.full_name,
            role=user_in.role,
            is_active=user_in.is_active
        )
        
        # 保存到数据库
        self.db.add(user)
        self.db.commit()
        self.db.refresh(user)
        
        return user
    
    def update(self, user: User, user_in: UserUpdate) -> User:
        """更新用户"""
        update_data = user_in.dict(exclude_unset=True)
        
        for field, value in update_data.items():
            setattr(user, field, value)
        
        self.db.commit()
        self.db.refresh(user)
        
        return user
    
    def update_password(self, user: User, new_password: str) -> User:
        """更新用户密码"""
        user.password_hash = get_password_hash(new_password)
        self.db.commit()
        self.db.refresh(user)
        
        return user
    
    def authenticate(self, username: str, password: str) -> Optional[User]:
        """验证用户凭据"""
        user = self.get_by_username(username)
        
        if not user:
            return None
        
        if not verify_password(password, user.password_hash):
            return None
        
        # 更新登录信息
        user.last_login = datetime.utcnow()
        user.login_count += 1
        self.db.commit()
        
        return user
    
    def activate(self, user: User) -> User:
        """激活用户"""
        user.is_active = True
        self.db.commit()
        self.db.refresh(user)
        
        return user
    
    def deactivate(self, user: User) -> User:
        """停用用户"""
        user.is_active = False
        self.db.commit()
        self.db.refresh(user)
        
        return user
    
    def delete(self, user: User) -> bool:
        """删除用户"""
        self.db.delete(user)
        self.db.commit()
        
        return True
    
    def is_admin(self, user: User) -> bool:
        """检查用户是否为管理员"""
        return user.role == UserRole.ADMIN
    
    def is_active(self, user: User) -> bool:
        """检查用户是否激活"""
        return user.is_active
    
    def can_manage_users(self, user: User) -> bool:
        """检查用户是否可以管理其他用户"""
        return user.role == UserRole.ADMIN
    
    def can_annotate(self, user: User) -> bool:
        """检查用户是否可以进行标注"""
        return user.role in [UserRole.ADMIN, UserRole.ANNOTATOR, UserRole.REVIEWER]
    
    def can_review(self, user: User) -> bool:
        """检查用户是否可以审核"""
        return user.role in [UserRole.ADMIN, UserRole.REVIEWER]

    def get_statistics(self) -> dict:
        """获取用户统计信息"""
        total_users = self.count()
        active_users = self.db.query(func.count(User.user_id)).filter(User.is_active == True).scalar()

        # 按角色统计
        role_stats = {}
        for role in UserRole:
            count = self.db.query(func.count(User.user_id)).filter(User.role == role.value).scalar()
            role_stats[role.value] = count

        return {
            "total_users": total_users,
            "active_users": active_users,
            "inactive_users": total_users - active_users,
            "role_distribution": role_stats
        }

    def validate_user_data(self, user_data: dict) -> tuple[bool, list[str]]:
        """验证用户数据"""
        errors = []

        # 检查必填字段
        required_fields = ["username", "email", "password"]
        for field in required_fields:
            if not user_data.get(field):
                errors.append(f"{field} is required")

        # 检查用户名长度
        username = user_data.get("username", "")
        if len(username) < 3:
            errors.append("Username must be at least 3 characters long")

        # 检查邮箱格式
        email = user_data.get("email", "")
        if email and "@" not in email:
            errors.append("Invalid email format")

        # 检查密码长度
        password = user_data.get("password", "")
        if len(password) < 6:
            errors.append("Password must be at least 6 characters long")

        # 检查角色有效性
        role = user_data.get("role")
        if role and role not in [r.value for r in UserRole]:
            errors.append("Invalid role")

        return len(errors) == 0, errors
