"""
知识点服务单元测试
"""

import pytest
from unittest.mock import Mock, patch
from sqlalchemy.orm import Session

from app.services.knowledge_service import KnowledgeService
from app.models.knowledge import KnowledgePoint, PrerequisiteRelation
from app.schemas.knowledge import KnowledgePointCreate, KnowledgePointUpdate
from tests.utils.factories import (
    KnowledgePointFactory,
    PrerequisiteRelationFactory,
    UserFactory,
    create_test_knowledge_hierarchy
)


@pytest.mark.unit
@pytest.mark.db
class TestKnowledgeService:
    """知识点服务测试类"""
    
    def test_get_knowledge_point_by_id(self, db: Session):
        """测试根据ID获取知识点"""
        KnowledgePointFactory._meta.sqlalchemy_session = db
        kp = KnowledgePointFactory()
        
        service = KnowledgeService(db)
        result = service.get(kp.kp_id)
        
        assert result is not None
        assert result.kp_id == kp.kp_id
        assert result.name == kp.name
        assert result.code == kp.code
    
    def test_get_knowledge_point_by_code(self, db: Session):
        """测试根据编码获取知识点"""
        KnowledgePointFactory._meta.sqlalchemy_session = db
        kp = KnowledgePointFactory(code="TEST_CODE_001")
        
        service = KnowledgeService(db)
        result = service.get_by_code("TEST_CODE_001")
        
        assert result is not None
        assert result.code == "TEST_CODE_001"
        assert result.kp_id == kp.kp_id
    
    def test_get_knowledge_point_not_found(self, db: Session):
        """测试获取不存在的知识点"""
        service = KnowledgeService(db)
        result = service.get(99999)
        
        assert result is None
    
    def test_get_knowledge_points_list(self, db: Session):
        """测试获取知识点列表"""
        KnowledgePointFactory._meta.sqlalchemy_session = db
        
        # 创建多个知识点
        kps = [KnowledgePointFactory() for _ in range(5)]
        
        service = KnowledgeService(db)
        result = service.get_multi(skip=0, limit=10)
        
        assert len(result) == 5
        assert all(isinstance(kp, KnowledgePoint) for kp in result)
    
    def test_get_knowledge_points_with_pagination(self, db: Session):
        """测试分页获取知识点"""
        KnowledgePointFactory._meta.sqlalchemy_session = db
        
        # 创建10个知识点
        kps = [KnowledgePointFactory() for _ in range(10)]
        
        service = KnowledgeService(db)
        
        # 测试第一页
        page1 = service.get_multi(skip=0, limit=5)
        assert len(page1) == 5
        
        # 测试第二页
        page2 = service.get_multi(skip=5, limit=5)
        assert len(page2) == 5
        
        # 确保没有重复
        page1_ids = {kp.kp_id for kp in page1}
        page2_ids = {kp.kp_id for kp in page2}
        assert page1_ids.isdisjoint(page2_ids)
    
    def test_search_knowledge_points(self, db: Session):
        """测试搜索知识点"""
        KnowledgePointFactory._meta.sqlalchemy_session = db
        
        # 创建特定名称的知识点
        kp1 = KnowledgePointFactory(name="线性代数基础", code="LINEAR_ALGEBRA")
        kp2 = KnowledgePointFactory(name="微积分入门", code="CALCULUS")
        kp3 = KnowledgePointFactory(name="代数几何", code="ALGEBRAIC_GEOMETRY")
        
        service = KnowledgeService(db)
        
        # 搜索包含"代数"的知识点
        results = service.get_multi(search="代数")
        result_names = [kp.name for kp in results]
        
        assert "线性代数基础" in result_names
        assert "代数几何" in result_names
        assert "微积分入门" not in result_names
    
    def test_filter_knowledge_points_by_subject(self, db: Session):
        """测试按学科过滤知识点"""
        KnowledgePointFactory._meta.sqlalchemy_session = db
        
        # 创建不同学科的知识点
        math_kp = KnowledgePointFactory(subject="数学")
        physics_kp = KnowledgePointFactory(subject="物理")
        chemistry_kp = KnowledgePointFactory(subject="化学")
        
        service = KnowledgeService(db)
        
        # 过滤数学知识点
        math_results = service.get_multi(filters={"subject": "数学"})
        assert len(math_results) == 1
        assert math_results[0].subject == "数学"
    
    def test_count_knowledge_points(self, db: Session):
        """测试统计知识点数量"""
        KnowledgePointFactory._meta.sqlalchemy_session = db
        
        # 创建知识点
        kps = [KnowledgePointFactory() for _ in range(7)]
        
        service = KnowledgeService(db)
        count = service.count()
        
        assert count == 7
    
    def test_count_knowledge_points_with_search(self, db: Session):
        """测试带搜索条件的知识点统计"""
        KnowledgePointFactory._meta.sqlalchemy_session = db
        
        # 创建知识点
        kp1 = KnowledgePointFactory(name="数学基础")
        kp2 = KnowledgePointFactory(name="物理基础")
        kp3 = KnowledgePointFactory(name="数学应用")
        
        service = KnowledgeService(db)
        count = service.count(search="数学")
        
        assert count == 2
    
    def test_create_knowledge_point(self, db: Session):
        """测试创建知识点"""
        UserFactory._meta.sqlalchemy_session = db
        user = UserFactory()
        
        kp_data = KnowledgePointCreate(
            name="新知识点",
            code="NEW_KP_001",
            description="这是一个新的知识点",
            subject="数学",
            grade="高中",
            difficulty=3
        )
        
        service = KnowledgeService(db)
        result = service.create(kp_data, creator_id=user.id)
        
        assert result.name == "新知识点"
        assert result.code == "NEW_KP_001"
        assert result.created_by == user.id
        assert result.updated_by == user.id
        assert result.is_leaf is True
        assert result.path == "NEW_KP_001"
    
    def test_create_child_knowledge_point(self, db: Session):
        """测试创建子知识点"""
        KnowledgePointFactory._meta.sqlalchemy_session = db
        UserFactory._meta.sqlalchemy_session = db
        
        parent = KnowledgePointFactory(code="PARENT", path="PARENT")
        user = UserFactory()
        
        child_data = KnowledgePointCreate(
            name="子知识点",
            code="CHILD",
            parent_id=parent.kp_id,
            subject="数学"
        )
        
        service = KnowledgeService(db)
        result = service.create(child_data, creator_id=user.id)
        
        assert result.parent_id == parent.kp_id
        assert result.path == "PARENT.CHILD"
        assert result.is_leaf is True
        
        # 验证父节点状态更新
        db.refresh(parent)
        assert parent.is_leaf is False
    
    def test_create_knowledge_point_with_invalid_parent(self, db: Session):
        """测试创建知识点时父节点不存在"""
        UserFactory._meta.sqlalchemy_session = db
        user = UserFactory()
        
        kp_data = KnowledgePointCreate(
            name="孤儿知识点",
            code="ORPHAN",
            parent_id=99999,  # 不存在的父节点
            subject="数学"
        )
        
        service = KnowledgeService(db)
        
        with pytest.raises(ValueError, match="父知识点不存在"):
            service.create(kp_data, creator_id=user.id)
    
    def test_update_knowledge_point(self, db: Session):
        """测试更新知识点"""
        KnowledgePointFactory._meta.sqlalchemy_session = db
        UserFactory._meta.sqlalchemy_session = db
        
        kp = KnowledgePointFactory(name="原始名称")
        user = UserFactory()
        
        update_data = KnowledgePointUpdate(
            name="更新后的名称",
            description="更新后的描述",
            difficulty=5
        )
        
        service = KnowledgeService(db)
        result = service.update(kp.kp_id, update_data, updater_id=user.id)
        
        assert result.name == "更新后的名称"
        assert result.description == "更新后的描述"
        assert result.difficulty == 5
        assert result.updated_by == user.id
    
    def test_update_nonexistent_knowledge_point(self, db: Session):
        """测试更新不存在的知识点"""
        UserFactory._meta.sqlalchemy_session = db
        user = UserFactory()
        
        update_data = KnowledgePointUpdate(name="新名称")
        
        service = KnowledgeService(db)
        result = service.update(99999, update_data, updater_id=user.id)
        
        assert result is None
    
    def test_delete_knowledge_point(self, db: Session):
        """测试删除知识点"""
        KnowledgePointFactory._meta.sqlalchemy_session = db
        kp = KnowledgePointFactory()
        kp_id = kp.kp_id
        
        service = KnowledgeService(db)
        result = service.delete(kp_id)
        
        assert result is True
        
        # 验证知识点已被删除
        deleted_kp = service.get(kp_id)
        assert deleted_kp is None
    
    def test_delete_nonexistent_knowledge_point(self, db: Session):
        """测试删除不存在的知识点"""
        service = KnowledgeService(db)
        result = service.delete(99999)
        
        assert result is False
    
    def test_get_knowledge_tree(self, db: Session):
        """测试获取知识点树形结构"""
        hierarchy = create_test_knowledge_hierarchy(db, "数学")
        
        service = KnowledgeService(db)
        tree = service.get_tree(subject="数学")
        
        assert len(tree) == 1  # 一个根节点
        root_node = tree[0]
        assert root_node.name == "数学基础"
        assert len(root_node.children) == 2  # 两个子节点
    
    def test_get_children_knowledge_points(self, db: Session):
        """测试获取子知识点"""
        hierarchy = create_test_knowledge_hierarchy(db, "数学")
        root, child1, child2 = hierarchy
        
        service = KnowledgeService(db)
        children = service.get_children(root.kp_id)
        
        assert len(children) == 2
        child_ids = {child.kp_id for child in children}
        assert child1.kp_id in child_ids
        assert child2.kp_id in child_ids
    
    def test_get_ancestors_knowledge_points(self, db: Session):
        """测试获取祖先知识点"""
        hierarchy = create_test_knowledge_hierarchy(db, "数学")
        root, child1, child2 = hierarchy
        
        service = KnowledgeService(db)
        ancestors = service.get_ancestors(child1.kp_id)
        
        assert len(ancestors) == 1
        assert ancestors[0].kp_id == root.kp_id
    
    def test_validate_knowledge_point_data(self, db: Session):
        """测试知识点数据验证"""
        service = KnowledgeService(db)
        
        # 测试有效数据
        valid_data = {
            "name": "有效知识点",
            "code": "VALID_001",
            "subject": "数学",
            "difficulty": 3
        }
        
        is_valid, errors = service.validate_knowledge_point_data(valid_data)
        assert is_valid is True
        assert len(errors) == 0
        
        # 测试无效数据
        invalid_data = {
            "name": "",  # 空名称
            "code": "",  # 空编码
            "difficulty": 10  # 超出范围
        }
        
        is_valid, errors = service.validate_knowledge_point_data(invalid_data)
        assert is_valid is False
        assert len(errors) > 0
