"""
标注服务单元测试
"""

import pytest
from datetime import datetime, timedelta
from unittest.mock import Mock, patch
from sqlalchemy.orm import Session

from app.services.annotation_service import AnnotationService
from app.models.annotation import AnnotationTask, AnnotationLog
from app.schemas.annotation import Annota<PERSON><PERSON><PERSON><PERSON><PERSON>, AnnotationTaskUpdate
from tests.utils.factories import (
    AnnotationTaskFactory,
    AnnotationLogFactory,
    UserFactory,
    QuestionFactory
)


@pytest.mark.unit
@pytest.mark.db
class TestAnnotationService:
    """标注服务测试类"""
    
    def test_get_annotation_task_by_id(self, db: Session):
        """测试根据ID获取标注任务"""
        AnnotationTaskFactory._meta.sqlalchemy_session = db
        task = AnnotationTaskFactory()
        
        service = AnnotationService(db)
        result = service.get_task(task.task_id)
        
        assert result is not None
        assert result.task_id == task.task_id
        assert result.title == task.title
        assert result.task_type == task.task_type
    
    def test_get_annotation_task_not_found(self, db: Session):
        """测试获取不存在的标注任务"""
        service = AnnotationService(db)
        result = service.get_task(99999)
        
        assert result is None
    
    def test_get_annotation_tasks_list(self, db: Session):
        """测试获取标注任务列表"""
        AnnotationTaskFactory._meta.sqlalchemy_session = db
        
        # 创建多个标注任务
        tasks = [AnnotationTaskFactory() for _ in range(5)]
        
        service = AnnotationService(db)
        result = service.get_tasks(skip=0, limit=10)
        
        assert len(result) == 5
        assert all(isinstance(task, AnnotationTask) for task in result)
    
    def test_get_tasks_by_assignee(self, db: Session):
        """测试获取指定用户的标注任务"""
        AnnotationTaskFactory._meta.sqlalchemy_session = db
        UserFactory._meta.sqlalchemy_session = db
        
        user1 = UserFactory()
        user2 = UserFactory()
        
        # 创建分配给不同用户的任务
        task1 = AnnotationTaskFactory(assigned_to=user1.id)
        task2 = AnnotationTaskFactory(assigned_to=user1.id)
        task3 = AnnotationTaskFactory(assigned_to=user2.id)
        
        service = AnnotationService(db)
        user1_tasks = service.get_tasks_by_assignee(user1.id)
        
        assert len(user1_tasks) == 2
        assert all(task.assigned_to == user1.id for task in user1_tasks)
    
    def test_get_tasks_by_status(self, db: Session):
        """测试按状态获取标注任务"""
        AnnotationTaskFactory._meta.sqlalchemy_session = db
        
        # 创建不同状态的任务
        pending_task = AnnotationTaskFactory(status="pending")
        in_progress_task = AnnotationTaskFactory(status="in_progress")
        completed_task = AnnotationTaskFactory(status="completed")
        
        service = AnnotationService(db)
        pending_tasks = service.get_tasks_by_status("pending")
        
        assert len(pending_tasks) == 1
        assert pending_tasks[0].status == "pending"
    
    def test_get_tasks_by_type(self, db: Session):
        """测试按类型获取标注任务"""
        AnnotationTaskFactory._meta.sqlalchemy_session = db
        
        # 创建不同类型的任务
        mapping_task = AnnotationTaskFactory(task_type="knowledge_point_mapping")
        rating_task = AnnotationTaskFactory(task_type="difficulty_rating")
        review_task = AnnotationTaskFactory(task_type="quality_review")
        
        service = AnnotationService(db)
        mapping_tasks = service.get_tasks_by_type("knowledge_point_mapping")
        
        assert len(mapping_tasks) == 1
        assert mapping_tasks[0].task_type == "knowledge_point_mapping"
    
    def test_create_annotation_task(self, db: Session):
        """测试创建标注任务"""
        UserFactory._meta.sqlalchemy_session = db
        creator = UserFactory()
        assignee = UserFactory()
        
        task_data = AnnotationTaskCreate(
            title="新标注任务",
            description="这是一个新的标注任务",
            task_type="knowledge_point_mapping",
            priority="medium",
            deadline=datetime.utcnow() + timedelta(days=7),
            assigned_to=assignee.id
        )
        
        service = AnnotationService(db)
        result = service.create_task(task_data, creator_id=creator.id)
        
        assert result.title == "新标注任务"
        assert result.task_type == "knowledge_point_mapping"
        assert result.priority == "medium"
        assert result.status == "pending"
        assert result.created_by == creator.id
        assert result.assigned_to == assignee.id
    
    def test_update_annotation_task(self, db: Session):
        """测试更新标注任务"""
        AnnotationTaskFactory._meta.sqlalchemy_session = db
        UserFactory._meta.sqlalchemy_session = db
        
        task = AnnotationTaskFactory(title="原始标题", status="pending")
        user = UserFactory()
        
        update_data = AnnotationTaskUpdate(
            title="更新后的标题",
            status="in_progress",
            priority="high"
        )
        
        service = AnnotationService(db)
        result = service.update_task(task.task_id, update_data, updater_id=user.user_id)
        
        assert result.title == "更新后的标题"
        assert result.status == "in_progress"
        assert result.priority == "high"
    
    def test_assign_task(self, db: Session):
        """测试分配标注任务"""
        AnnotationTaskFactory._meta.sqlalchemy_session = db
        UserFactory._meta.sqlalchemy_session = db
        
        task = AnnotationTaskFactory(assigned_to=None)
        user = UserFactory()
        
        service = AnnotationService(db)
        result = service.assign_task(task.task_id, user.user_id)
        
        assert result.assigned_to == user.user_id
        assert result.status == "pending"
    
    def test_start_task(self, db: Session):
        """测试开始标注任务"""
        AnnotationTaskFactory._meta.sqlalchemy_session = db
        UserFactory._meta.sqlalchemy_session = db
        
        task = AnnotationTaskFactory(status="pending")
        user = UserFactory()
        
        service = AnnotationService(db)
        result = service.start_task(task.task_id, user.user_id)
        
        assert result.status == "in_progress"
        assert result.started_at is not None
        
        # 验证日志记录
        logs = db.query(AnnotationLog).filter(
            AnnotationLog.task_id == task.task_id,
            AnnotationLog.action == "start"
        ).all()
        assert len(logs) == 1
    
    def test_complete_task(self, db: Session):
        """测试完成标注任务"""
        AnnotationTaskFactory._meta.sqlalchemy_session = db
        UserFactory._meta.sqlalchemy_session = db
        
        task = AnnotationTaskFactory(status="in_progress")
        user = UserFactory()
        
        completion_data = {
            "result": {"knowledge_points": ["KP001", "KP002"]},
            "notes": "标注完成"
        }
        
        service = AnnotationService(db)
        result = service.complete_task(task.task_id, user.user_id, completion_data)
        
        assert result.status == "completed"
        assert result.completed_at is not None
        assert result.result == completion_data["result"]
        
        # 验证日志记录
        logs = db.query(AnnotationLog).filter(
            AnnotationLog.task_id == task.task_id,
            AnnotationLog.action == "complete"
        ).all()
        assert len(logs) == 1
    
    def test_cancel_task(self, db: Session):
        """测试取消标注任务"""
        AnnotationTaskFactory._meta.sqlalchemy_session = db
        UserFactory._meta.sqlalchemy_session = db
        
        task = AnnotationTaskFactory(status="pending")
        user = UserFactory()
        
        service = AnnotationService(db)
        result = service.cancel_task(task.task_id, user.user_id, "任务取消")
        
        assert result.status == "cancelled"
        
        # 验证日志记录
        logs = db.query(AnnotationLog).filter(
            AnnotationLog.task_id == task.task_id,
            AnnotationLog.action == "cancel"
        ).all()
        assert len(logs) == 1
    
    def test_get_task_logs(self, db: Session):
        """测试获取任务日志"""
        AnnotationTaskFactory._meta.sqlalchemy_session = db
        AnnotationLogFactory._meta.sqlalchemy_session = db
        
        task = AnnotationTaskFactory()
        
        # 创建多个日志
        logs = [
            AnnotationLogFactory(task_id=task.task_id, action="start"),
            AnnotationLogFactory(task_id=task.task_id, action="update"),
            AnnotationLogFactory(task_id=task.task_id, action="complete")
        ]
        
        service = AnnotationService(db)
        result = service.get_task_logs(task.task_id)
        
        assert len(result) == 3
        assert all(log.task_id == task.task_id for log in result)
        
        # 验证按时间排序
        for i in range(1, len(result)):
            assert result[i].created_at >= result[i-1].created_at
    
    def test_add_task_log(self, db: Session):
        """测试添加任务日志"""
        AnnotationTaskFactory._meta.sqlalchemy_session = db
        UserFactory._meta.sqlalchemy_session = db
        
        task = AnnotationTaskFactory()
        user = UserFactory()
        
        service = AnnotationService(db)
        result = service.add_task_log(
            task.task_id,
            user.user_id,
            "comment",
            "这是一个评论",
            {"rating": 5}
        )
        
        assert result.task_id == task.task_id
        assert result.annotator_id == user.user_id
        assert result.action == "comment"
        assert result.content == "这是一个评论"
        assert result.metadata["rating"] == 5
    
    def test_get_task_statistics(self, db: Session):
        """测试获取任务统计信息"""
        AnnotationTaskFactory._meta.sqlalchemy_session = db
        
        # 创建不同状态的任务
        AnnotationTaskFactory(status="pending")
        AnnotationTaskFactory(status="pending")
        AnnotationTaskFactory(status="in_progress")
        AnnotationTaskFactory(status="completed")
        AnnotationTaskFactory(status="cancelled")
        
        service = AnnotationService(db)
        stats = service.get_task_statistics()
        
        assert stats["total"] == 5
        assert stats["by_status"]["pending"] == 2
        assert stats["by_status"]["in_progress"] == 1
        assert stats["by_status"]["completed"] == 1
        assert stats["by_status"]["cancelled"] == 1
    
    def test_get_user_task_statistics(self, db: Session):
        """测试获取用户任务统计"""
        AnnotationTaskFactory._meta.sqlalchemy_session = db
        UserFactory._meta.sqlalchemy_session = db
        
        user = UserFactory()
        
        # 创建用户的任务
        AnnotationTaskFactory(assigned_to=user.user_id, status="pending")
        AnnotationTaskFactory(assigned_to=user.user_id, status="completed")
        AnnotationTaskFactory(assigned_to=user.user_id, status="completed")
        
        service = AnnotationService(db)
        stats = service.get_user_task_statistics(user.user_id)
        
        assert stats["total"] == 3
        assert stats["pending"] == 1
        assert stats["completed"] == 2
        assert stats["completion_rate"] == 2/3
    
    def test_get_overdue_tasks(self, db: Session):
        """测试获取逾期任务"""
        AnnotationTaskFactory._meta.sqlalchemy_session = db
        
        # 创建逾期和未逾期的任务
        overdue_task = AnnotationTaskFactory(
            deadline=datetime.utcnow() - timedelta(days=1),
            status="pending"
        )
        future_task = AnnotationTaskFactory(
            deadline=datetime.utcnow() + timedelta(days=1),
            status="pending"
        )
        completed_overdue = AnnotationTaskFactory(
            deadline=datetime.utcnow() - timedelta(days=1),
            status="completed"
        )
        
        service = AnnotationService(db)
        overdue_tasks = service.get_overdue_tasks()
        
        assert len(overdue_tasks) == 1
        assert overdue_tasks[0].task_id == overdue_task.task_id
    
    def test_bulk_assign_tasks(self, db: Session):
        """测试批量分配任务"""
        AnnotationTaskFactory._meta.sqlalchemy_session = db
        UserFactory._meta.sqlalchemy_session = db
        
        tasks = [AnnotationTaskFactory(assigned_to=None) for _ in range(3)]
        user = UserFactory()
        
        task_ids = [task.task_id for task in tasks]
        
        service = AnnotationService(db)
        result = service.bulk_assign_tasks(task_ids, user.user_id)
        
        assert result == 3
        
        # 验证所有任务都被分配
        for task in tasks:
            db.refresh(task)
            assert task.assigned_to == user.user_id
