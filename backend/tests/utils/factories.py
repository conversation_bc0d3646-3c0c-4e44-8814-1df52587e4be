"""
测试数据工厂
使用Factory Boy创建测试数据
"""

import factory
from factory.alchemy import SQLAlchemyModelFactory
from sqlalchemy.orm import Session
from datetime import datetime, timedelta
from typing import Optional

from app.models.user import User
from app.models.knowledge import KnowledgePoint, PrerequisiteRelation
from app.models.question import Question, QuestionAsset
from app.models.annotation import AnnotationTask, AnnotationLog
from app.models.mapping import ItemKpMap


class BaseFactory(SQLAlchemyModelFactory):
    """基础工厂类"""
    
    class Meta:
        abstract = True
        sqlalchemy_session_persistence = "commit"


class UserFactory(BaseFactory):
    """用户工厂"""
    
    class Meta:
        model = User
    
    username = factory.Sequence(lambda n: f"user{n}")
    email = factory.LazyAttribute(lambda obj: f"{obj.username}@example.com")
    full_name = factory.Faker("name")
    password_hash = "$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW"  # "secret"
    role = "annotator"
    is_active = True
    created_at = factory.LazyFunction(datetime.utcnow)
    updated_at = factory.LazyFunction(datetime.utcnow)


class AdminUserFactory(UserFactory):
    """管理员用户工厂"""
    
    role = "admin"
    username = factory.Sequence(lambda n: f"admin{n}")


class KnowledgePointFactory(BaseFactory):
    """知识点工厂"""
    
    class Meta:
        model = KnowledgePoint
    
    name = factory.Faker("sentence", nb_words=3)
    code = factory.Sequence(lambda n: f"KP{n:04d}")
    description = factory.Faker("text", max_nb_chars=200)
    difficulty_level = factory.Faker("random_int", min=1, max=5)
    is_leaf = True
    path = factory.LazyAttribute(lambda obj: obj.code)
    created_by = 1  # 默认用户ID
    updated_by = 1  # 默认用户ID
    created_at = factory.LazyFunction(datetime.utcnow)
    updated_at = factory.LazyFunction(datetime.utcnow)


class PrerequisiteRelationFactory(BaseFactory):
    """先修关系工厂"""
    
    class Meta:
        model = PrerequisiteRelation
    
    pre_kp = factory.SubFactory(KnowledgePointFactory)
    post_kp = factory.SubFactory(KnowledgePointFactory)
    relation_type = "prerequisite"
    strength = factory.Faker("random_element", elements=(0.5, 0.7, 0.8, 0.9, 1.0))
    created_by = factory.SubFactory(UserFactory)
    updated_by = factory.SelfAttribute("created_by")
    created_at = factory.LazyFunction(datetime.utcnow)
    updated_at = factory.LazyFunction(datetime.utcnow)


class QuestionFactory(BaseFactory):
    """题目工厂"""
    
    class Meta:
        model = Question
    
    content = factory.LazyFunction(lambda: {"title": factory.Faker("sentence", nb_words=5).generate(), "body": factory.Faker("text", max_nb_chars=500).generate()})
    q_type = factory.Faker("random_int", min=1, max=4)
    difficulty_lvl = factory.Faker("random_int", min=1, max=5)
    answer_key = factory.LazyFunction(lambda: {"correct": "A"})
    analysis = factory.Faker("text", max_nb_chars=200)
    created_by = 1  # 默认用户ID
    updated_by = 1  # 默认用户ID
    created_at = factory.LazyFunction(datetime.utcnow)
    updated_at = factory.LazyFunction(datetime.utcnow)


class SingleChoiceQuestionFactory(QuestionFactory):
    """单选题工厂"""

    q_type = 1  # 单选题
    answer = "A"
    
    @factory.post_generation
    def options(self, create, extracted, **kwargs):
        if not create:
            return
        
        # 创建选项
        self.options = [
            {"key": "A", "content": "选项A"},
            {"key": "B", "content": "选项B"},
            {"key": "C", "content": "选项C"},
            {"key": "D", "content": "选项D"}
        ]


class AnnotationTaskFactory(BaseFactory):
    """标注任务工厂"""
    
    class Meta:
        model = AnnotationTask
    
    title = factory.Faker("sentence", nb_words=4)
    description = factory.Faker("text", max_nb_chars=300)
    task_type = factory.Faker("random_element", 
                            elements=("knowledge_point_mapping", "difficulty_rating", "quality_review"))
    priority = factory.Faker("random_element", elements=("low", "medium", "high", "urgent"))
    status = "pending"
    deadline = factory.LazyFunction(lambda: datetime.utcnow() + timedelta(days=7))
    created_by = factory.SubFactory(UserFactory)
    assigned_to = factory.SubFactory(UserFactory)
    created_at = factory.LazyFunction(datetime.utcnow)
    updated_at = factory.LazyFunction(datetime.utcnow)


class AnnotationLogFactory(BaseFactory):
    """标注日志工厂"""
    
    class Meta:
        model = AnnotationLog
    
    task = factory.SubFactory(AnnotationTaskFactory)
    annotator = factory.SubFactory(UserFactory)
    action = factory.Faker("random_element", 
                         elements=("start", "submit", "review", "approve", "reject"))
    content = factory.Faker("text", max_nb_chars=200)
    created_at = factory.LazyFunction(datetime.utcnow)


class ItemKpMapFactory(BaseFactory):
    """题目知识点映射工厂"""
    
    class Meta:
        model = ItemKpMap
    
    question = factory.SubFactory(QuestionFactory)
    knowledge_point = factory.SubFactory(KnowledgePointFactory)
    relevance_score = factory.Faker("random_element", elements=(0.6, 0.7, 0.8, 0.9, 1.0))
    confidence = factory.Faker("random_element", elements=(0.7, 0.8, 0.9, 1.0))
    mapping_type = "manual"
    created_by = factory.SubFactory(UserFactory)
    updated_by = factory.SelfAttribute("created_by")
    created_at = factory.LazyFunction(datetime.utcnow)
    updated_at = factory.LazyFunction(datetime.utcnow)


def create_test_user(db: Session, **kwargs) -> User:
    """创建测试用户"""
    UserFactory._meta.sqlalchemy_session = db
    return UserFactory(**kwargs)


def create_test_admin(db: Session, **kwargs) -> User:
    """创建测试管理员"""
    AdminUserFactory._meta.sqlalchemy_session = db
    return AdminUserFactory(**kwargs)


def create_test_knowledge_point(db: Session, **kwargs) -> KnowledgePoint:
    """创建测试知识点"""
    KnowledgePointFactory._meta.sqlalchemy_session = db
    return KnowledgePointFactory(**kwargs)


def create_test_question(db: Session, **kwargs) -> Question:
    """创建测试题目"""
    QuestionFactory._meta.sqlalchemy_session = db
    return QuestionFactory(**kwargs)


def create_test_single_choice_question(db: Session, **kwargs) -> Question:
    """创建测试单选题"""
    SingleChoiceQuestionFactory._meta.sqlalchemy_session = db
    return SingleChoiceQuestionFactory(**kwargs)


def create_test_annotation_task(db: Session, **kwargs) -> AnnotationTask:
    """创建测试标注任务"""
    AnnotationTaskFactory._meta.sqlalchemy_session = db
    return AnnotationTaskFactory(**kwargs)


def create_test_knowledge_hierarchy(db: Session, subject: str = "数学") -> list[KnowledgePoint]:
    """创建测试知识点层次结构"""
    KnowledgePointFactory._meta.sqlalchemy_session = db
    PrerequisiteRelationFactory._meta.sqlalchemy_session = db
    
    # 创建根知识点
    root = KnowledgePointFactory(
        name=f"{subject}基础",
        code=f"{subject.upper()}_ROOT",
        subject=subject,
        is_leaf=False,
        parent_id=None
    )
    
    # 创建子知识点
    child1 = KnowledgePointFactory(
        name=f"{subject}概念",
        code=f"{subject.upper()}_CONCEPT",
        subject=subject,
        parent_id=root.kp_id,
        path=f"{root.code}.{subject.upper()}_CONCEPT"
    )
    
    child2 = KnowledgePointFactory(
        name=f"{subject}应用",
        code=f"{subject.upper()}_APPLICATION",
        subject=subject,
        parent_id=root.kp_id,
        path=f"{root.code}.{subject.upper()}_APPLICATION"
    )
    
    # 创建先修关系
    PrerequisiteRelationFactory(
        pre_kp=child1,
        post_kp=child2
    )
    
    return [root, child1, child2]
