"""
知识点API端点测试
"""

import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from tests.utils.factories import (
    KnowledgePointFactory,
    UserFactory,
    create_test_knowledge_hierarchy
)
from tests.utils.helpers import (
    create_auth_headers,
    assert_response_success,
    assert_response_error,
    assert_pagination_response
)


@pytest.mark.api
@pytest.mark.db
class TestKnowledgePointsAPI:
    """知识点API测试类"""
    
    def test_get_knowledge_points_list(self, client: TestClient, db: Session):
        """测试获取知识点列表"""
        KnowledgePointFactory._meta.sqlalchemy_session = db
        UserFactory._meta.sqlalchemy_session = db
        
        # 创建测试数据
        user = UserFactory()
        kps = [KnowledgePointFactory() for _ in range(5)]
        
        # 认证
        headers = create_auth_headers(user)
        
        response = client.get("/api/v1/knowledge-points/", headers=headers)
        
        assert_response_success(response, 200)
        data = response.json()
        
        assert_pagination_response(data, expected_total=5)
        assert len(data["items"]) == 5
    
    def test_get_knowledge_points_with_pagination(self, client: TestClient, db: Session):
        """测试分页获取知识点"""
        KnowledgePointFactory._meta.sqlalchemy_session = db
        UserFactory._meta.sqlalchemy_session = db
        
        user = UserFactory()
        kps = [KnowledgePointFactory() for _ in range(10)]
        
        headers = create_auth_headers(user)
        
        # 获取第一页
        response = client.get(
            "/api/v1/knowledge-points/?skip=0&limit=5",
            headers=headers
        )
        
        assert_response_success(response, 200)
        data = response.json()
        
        assert data["total"] == 10
        assert len(data["items"]) == 5
        assert data["skip"] == 0
        assert data["limit"] == 5
    
    def test_get_knowledge_points_with_search(self, client: TestClient, db: Session):
        """测试搜索知识点"""
        KnowledgePointFactory._meta.sqlalchemy_session = db
        UserFactory._meta.sqlalchemy_session = db
        
        user = UserFactory()
        
        # 创建特定名称的知识点
        kp1 = KnowledgePointFactory(name="线性代数基础")
        kp2 = KnowledgePointFactory(name="微积分入门")
        kp3 = KnowledgePointFactory(name="代数几何")
        
        headers = create_auth_headers(user)
        
        response = client.get(
            "/api/v1/knowledge-points/?search=代数",
            headers=headers
        )
        
        assert_response_success(response, 200)
        data = response.json()
        
        assert data["total"] == 2
        names = [item["name"] for item in data["items"]]
        assert "线性代数基础" in names
        assert "代数几何" in names
        assert "微积分入门" not in names
    
    def test_get_knowledge_points_with_filters(self, client: TestClient, db: Session):
        """测试过滤知识点"""
        KnowledgePointFactory._meta.sqlalchemy_session = db
        UserFactory._meta.sqlalchemy_session = db
        
        user = UserFactory()
        
        # 创建不同学科的知识点
        math_kp = KnowledgePointFactory(subject="数学")
        physics_kp = KnowledgePointFactory(subject="物理")
        
        headers = create_auth_headers(user)
        
        response = client.get(
            "/api/v1/knowledge-points/?subject=数学",
            headers=headers
        )
        
        assert_response_success(response, 200)
        data = response.json()
        
        assert data["total"] == 1
        assert data["items"][0]["subject"] == "数学"
    
    def test_get_knowledge_point_by_id(self, client: TestClient, db: Session):
        """测试根据ID获取知识点"""
        KnowledgePointFactory._meta.sqlalchemy_session = db
        UserFactory._meta.sqlalchemy_session = db
        
        user = UserFactory()
        kp = KnowledgePointFactory(name="测试知识点")
        
        headers = create_auth_headers(user)
        
        response = client.get(
            f"/api/v1/knowledge-points/{kp.kp_id}",
            headers=headers
        )
        
        assert_response_success(response, 200)
        data = response.json()
        
        assert data["kp_id"] == kp.kp_id
        assert data["name"] == "测试知识点"
        assert data["code"] == kp.code
    
    def test_get_knowledge_point_not_found(self, client: TestClient, db: Session):
        """测试获取不存在的知识点"""
        UserFactory._meta.sqlalchemy_session = db
        user = UserFactory()
        
        headers = create_auth_headers(user)
        
        response = client.get(
            "/api/v1/knowledge-points/99999",
            headers=headers
        )
        
        assert_response_error(response, 404, "知识点不存在")
    
    def test_create_knowledge_point(self, client: TestClient, db: Session):
        """测试创建知识点"""
        UserFactory._meta.sqlalchemy_session = db
        user = UserFactory()
        
        kp_data = {
            "name": "新知识点",
            "code": "NEW_KP_001",
            "description": "这是一个新的知识点",
            "subject": "数学",
            "grade": "高中",
            "difficulty": 3
        }
        
        headers = create_auth_headers(user)
        
        response = client.post(
            "/api/v1/knowledge-points/",
            json=kp_data,
            headers=headers
        )
        
        assert_response_success(response, 201)
        data = response.json()
        
        assert data["name"] == "新知识点"
        assert data["code"] == "NEW_KP_001"
        assert data["subject"] == "数学"
        assert data["created_by"] == user.id
    
    def test_create_knowledge_point_with_parent(self, client: TestClient, db: Session):
        """测试创建子知识点"""
        KnowledgePointFactory._meta.sqlalchemy_session = db
        UserFactory._meta.sqlalchemy_session = db
        
        user = UserFactory()
        parent = KnowledgePointFactory()
        
        kp_data = {
            "name": "子知识点",
            "code": "CHILD_KP_001",
            "parent_id": parent.kp_id,
            "subject": "数学"
        }
        
        headers = create_auth_headers(user)
        
        response = client.post(
            "/api/v1/knowledge-points/",
            json=kp_data,
            headers=headers
        )
        
        assert_response_success(response, 201)
        data = response.json()
        
        assert data["parent_id"] == parent.kp_id
        assert data["path"] == f"{parent.path}.CHILD_KP_001"
    
    def test_create_knowledge_point_invalid_parent(self, client: TestClient, db: Session):
        """测试创建知识点时父节点不存在"""
        UserFactory._meta.sqlalchemy_session = db
        user = UserFactory()
        
        kp_data = {
            "name": "孤儿知识点",
            "code": "ORPHAN_001",
            "parent_id": 99999,
            "subject": "数学"
        }
        
        headers = create_auth_headers(user)
        
        response = client.post(
            "/api/v1/knowledge-points/",
            json=kp_data,
            headers=headers
        )
        
        assert_response_error(response, 400, "父知识点不存在")
    
    def test_create_knowledge_point_invalid_data(self, client: TestClient, db: Session):
        """测试创建知识点时数据无效"""
        UserFactory._meta.sqlalchemy_session = db
        user = UserFactory()
        
        kp_data = {
            "name": "",  # 空名称
            "code": "",  # 空编码
            "difficulty": 10  # 超出范围
        }
        
        headers = create_auth_headers(user)
        
        response = client.post(
            "/api/v1/knowledge-points/",
            json=kp_data,
            headers=headers
        )
        
        assert response.status_code == 422  # 验证错误
    
    def test_update_knowledge_point(self, client: TestClient, db: Session):
        """测试更新知识点"""
        KnowledgePointFactory._meta.sqlalchemy_session = db
        UserFactory._meta.sqlalchemy_session = db
        
        user = UserFactory()
        kp = KnowledgePointFactory(name="原始名称")
        
        update_data = {
            "name": "更新后的名称",
            "description": "更新后的描述",
            "difficulty": 5
        }
        
        headers = create_auth_headers(user)
        
        response = client.put(
            f"/api/v1/knowledge-points/{kp.kp_id}",
            json=update_data,
            headers=headers
        )
        
        assert_response_success(response, 200)
        data = response.json()
        
        assert data["name"] == "更新后的名称"
        assert data["description"] == "更新后的描述"
        assert data["difficulty"] == 5
    
    def test_update_knowledge_point_not_found(self, client: TestClient, db: Session):
        """测试更新不存在的知识点"""
        UserFactory._meta.sqlalchemy_session = db
        user = UserFactory()
        
        update_data = {"name": "新名称"}
        headers = create_auth_headers(user)
        
        response = client.put(
            "/api/v1/knowledge-points/99999",
            json=update_data,
            headers=headers
        )
        
        assert_response_error(response, 404, "知识点不存在")
    
    def test_delete_knowledge_point(self, client: TestClient, db: Session):
        """测试删除知识点"""
        KnowledgePointFactory._meta.sqlalchemy_session = db
        UserFactory._meta.sqlalchemy_session = db
        
        user = UserFactory()
        kp = KnowledgePointFactory()
        
        headers = create_auth_headers(user)
        
        response = client.delete(
            f"/api/v1/knowledge-points/{kp.kp_id}",
            headers=headers
        )
        
        assert_response_success(response, 200)
        data = response.json()
        
        assert data["message"] == "知识点删除成功"
    
    def test_delete_knowledge_point_not_found(self, client: TestClient, db: Session):
        """测试删除不存在的知识点"""
        UserFactory._meta.sqlalchemy_session = db
        user = UserFactory()
        
        headers = create_auth_headers(user)
        
        response = client.delete(
            "/api/v1/knowledge-points/99999",
            headers=headers
        )
        
        assert_response_error(response, 404, "知识点不存在")
    
    def test_get_knowledge_tree(self, client: TestClient, db: Session):
        """测试获取知识点树形结构"""
        UserFactory._meta.sqlalchemy_session = db
        user = UserFactory()
        
        # 创建知识点层次结构
        hierarchy = create_test_knowledge_hierarchy(db, "数学")
        
        headers = create_auth_headers(user)
        
        response = client.get(
            "/api/v1/knowledge-points/tree?subject=数学",
            headers=headers
        )
        
        assert_response_success(response, 200)
        data = response.json()
        
        assert "tree" in data
        assert len(data["tree"]) == 1  # 一个根节点
        
        root_node = data["tree"][0]
        assert root_node["name"] == "数学基础"
        assert len(root_node["children"]) == 2  # 两个子节点
    
    def test_get_knowledge_tree_with_subject_filter(self, client: TestClient, db: Session):
        """测试按学科过滤知识点树"""
        UserFactory._meta.sqlalchemy_session = db
        user = UserFactory()
        
        # 创建不同学科的知识点层次
        math_hierarchy = create_test_knowledge_hierarchy(db, "数学")
        physics_hierarchy = create_test_knowledge_hierarchy(db, "物理")
        
        headers = create_auth_headers(user)
        
        response = client.get(
            "/api/v1/knowledge-points/tree?subject=数学",
            headers=headers
        )
        
        assert_response_success(response, 200)
        data = response.json()
        
        assert len(data["tree"]) == 1
        assert data["tree"][0]["subject"] == "数学"
    
    def test_unauthorized_access(self, client: TestClient, db: Session):
        """测试未授权访问"""
        response = client.get("/api/v1/knowledge-points/")
        
        assert_response_error(response, 401, "未提供认证凭据")
    
    def test_insufficient_permissions(self, client: TestClient, db: Session):
        """测试权限不足"""
        UserFactory._meta.sqlalchemy_session = db
        
        # 创建普通用户（非管理员）
        user = UserFactory(role="viewer")
        headers = create_auth_headers(user)
        
        kp_data = {
            "name": "新知识点",
            "code": "NEW_001",
            "subject": "数学"
        }
        
        # 尝试创建知识点（需要更高权限）
        response = client.post(
            "/api/v1/knowledge-points/",
            json=kp_data,
            headers=headers
        )
        
        # 根据实际权限设置，这里可能返回403或其他状态码
        assert response.status_code in [403, 401]
